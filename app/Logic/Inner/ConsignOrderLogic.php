<?php

namespace App\Logic\Inner;

use App\Constants\BusinessConst;
use App\Constants\ConsignOrderConst;
use App\Constants\OrderConst;
use App\Constants\TemplateConst;
use App\ErrCode\BaseErr;
use App\Exceptions\ErrException;
use App\Logic\AppraiserGroupLogic;
use App\Models\OrderModel;
use App\Service\AppraiserGroupService;
use App\Service\BusinessCategoryService;
use App\Service\OrderService;
use App\Utils\Production;
use App\Utils\Singleton;
use Illuminate\Database\Query\Builder;

/**
 * 寄售订单
 */
class ConsignOrderLogic
{
    use Singleton;

    /**
     * 转派
     *
     * @param $params
     * @return array
     * @throws ErrException
     */
    public function transfer($params)
    {
        $subType = $params['subType'];
        $categoryIdentifier = $params['categoryIdentifier'];
        $appraiserId = $params['appraiserId'];
        $uri = $params['uri'];

        $order = OrderService::getInstance()->getOrderByUri($uri);
        if (empty($order)) {
            throw new ErrException(BaseErr::PARAMETER_ERROR);
        }
        $saveData = [
            'userinfo_id' => $appraiserId,
        ];
        $isTransferEntity = false;
        // 是否转实物
        if ($subType == ConsignOrderConst::CONSIGN_SUB_TYPE_ENTITY && $order->sub_type == ConsignOrderConst::CONSIGN_SUB_TYPE_PIC) {
            $isTransferEntity = true;
            $saveData['sub_type'] = ConsignOrderConst::CONSIGN_SUB_TYPE_ENTITY;
        } elseif ($categoryIdentifier != $order->category_identifier) {
            $category = BusinessCategoryService::getInstance()->getCategoryByIdentifier($categoryIdentifier, BusinessConst::BUSINESS_TYPE_CONSIGN_VERIFY);
            if (empty($category)) {
                throw new ErrException(BaseErr::PARAMETER_ERROR);
            }

            $saveData['category_id'] = $category->id;
            $saveData['category_identifier'] = $categoryIdentifier;
        }

        OrderService::getInstance()->updateById($order->id, $saveData);
        // 推送分配到鉴定师
        Production::push('image-ident-order-assign', ['uri' => $uri, 'businessId' => BusinessConst::BUSINESS_TYPE_CONSIGN_VERIFY, 'userinfoId' => $appraiserId, 'scene' => OrderConst::ASSIGN_SCENE_REASSIGN, 'env' => env('ENV', '')]);

        return [];
    }

    /**
     * 列表
     *
     * @param array $params
     * @return array
     */
    public function list(array $params)
    {
        $userinfoId = $params['userinfoId'];
        $page = $params['page'];
        $pageSize = $params['pageSize'];

        // -1:全部 0:待查验 1:通过 2:不通过
        $assessState = $params['assessState'] ?? ConsignOrderConst::ASSESS_STATE_ALL;
        // 0：全部 1：图文 2：实物 4：组内剩余 5：今日待查
        $subType = $params['subType'] ?? 0;

        $query = OrderModel::query();
        $query = $this->buildAssessState($query, $assessState);
        $query = $this->buildSubType($query, $subType, $userinfoId);
        $query = $this->buildSort($query, $subType, $assessState);

        $orderList = $query->forPage($page, $pageSize)->get();

        return [
            'list' => OrderLogic::getInstance()->formatList($orderList),
            'page' => ++$page,
            'isEnd' => $orderList->count() < $pageSize,
            'typeCount' => $this->getTypeCount($userinfoId, $assessState)
        ];
    }

    /**
     * 获取类型数量统计
     *
     * @param $userinfoId
     * @param $assessState
     * @return array
     */
    private function getTypeCount($userinfoId, $assessState)
    {
        $picCount = $this->getPicCount($userinfoId, $assessState);
        $entityCount = $this->getEntityCount($userinfoId, $assessState);
        $assessStayTodayCount = $this->getAssessStayTodayCount($userinfoId, $assessState);
        $groupSurplusCount = $this->getGroupSurplusCount($userinfoId, $assessState);
        $allCount = $this->getAllCount($userinfoId, $assessState);
        return [
            'allCount' => $allCount,
            'assessStayTodayCount' => $assessStayTodayCount,
            'entityCount' => $entityCount,
            'groupSurplusCount' => $groupSurplusCount,
            'picCount' => $picCount,
            'waitCount' => $this->getWaitCount($userinfoId),
        ];
    }

    private function getAllCount($userinfoId, $assessState)
    {
        $query = OrderModel::query();
        $query = $this->buildAssessState($query, $assessState);
        $query = $this->buildSubType($query, ConsignOrderConst::CONSIGN_SUB_TYPE_ALL, $userinfoId);
        return $query->count();
    }

    /**
     * 等待鉴定的数量
     *
     * @param $userinfoId
     * @return mixed
     */
    private function getWaitCount($userinfoId)
    {
        $picCount = $this->getPicCount($userinfoId, ConsignOrderConst::ASSESS_STATE_WAIT);
        $entityCount = $this->getEntityCount($userinfoId, ConsignOrderConst::ASSESS_STATE_WAIT);
        return $picCount + $entityCount;
    }

    /**
     * 组内剩余
     *
     * @param $userinfoId
     * @param $assessState
     * @return mixed
     */
    private function getGroupSurplusCount($userinfoId, $assessState)
    {
        $query = OrderModel::query();
        $query = $this->buildSubType($query, ConsignOrderConst::CONSIGN_SUB_TYPE_GROUP, $userinfoId);
        $query = $this->buildAssessState($query, $assessState);
        return $query->count();
    }

    /**
     * 今日待查
     *
     * @param $userinfoId
     * @param $assessState
     * @return mixed
     */
    private function getAssessStayTodayCount($userinfoId, $assessState)
    {
        $query = OrderModel::query();
        $query = $this->buildSubType($query, ConsignOrderConst::CONSIGN_SUB_TYPE_TODAY, $userinfoId);
        $query = $this->buildAssessState($query, $assessState);
        return $query->count();
    }

    /**
     * 获取实物数量
     *
     * @param $userinfoId
     * @param $assessState
     * @return mixed
     */
    private function getEntityCount($userinfoId, $assessState)
    {
        $query = OrderModel::query();
        $query = $this->buildSubType($query, ConsignOrderConst::CONSIGN_SUB_TYPE_ENTITY, $userinfoId);
        $query = $this->buildAssessState($query, $assessState);
        return $query->count();
    }

    /**
     * 获取图文数量
     *
     * @param $userinfoId
     * @param $assessState
     * @return mixed
     */
    private function getPicCount($userinfoId, $assessState)
    {
        $query = OrderModel::query();
        $query = $this->buildSubType($query, ConsignOrderConst::CONSIGN_SUB_TYPE_PIC, $userinfoId);
        $query = $this->buildAssessState($query, $assessState);
        return $query->count();
    }

    /**
     * @param Builder $query
     * @param $subType
     * @param $assessState
     * @return Builder
     */
    private function buildSort($query, $subType, $assessState)
    {
        /** var Builder $query */
        if ($assessState == ConsignOrderConst::ASSESS_STATE_WAIT) {
            if ($subType == ConsignOrderConst::CONSIGN_SUB_TYPE_ALL) {
                $query = $query->orderByRaw("FIELD(sub_type, '" . implode("','", [ConsignOrderConst::CONSIGN_SUB_TYPE_PIC, ConsignOrderConst::CONSIGN_SUB_TYPE_ENTITY]) . "')");
                $query = $query->orderBy('id', 'asc');
            }
        }
        return $query;
    }

    /**
     * 类型
     *
     * @param $query
     * @param $subType
     * @param $userinfoId
     * @return mixed
     */
    private function buildSubType($query, $subType, $userinfoId)
    {
        // 0：全部 1：图文 2：实物 4：组内剩余 5：今日待查
        $groupAppraiserIds = [$userinfoId];
        if ($subType == ConsignOrderConst::CONSIGN_SUB_TYPE_ALL) {
            return $query->whereIn('userinfo_id', [$userinfoId]);
        }
        // 1: 图文
        if ($subType == ConsignOrderConst::CONSIGN_SUB_TYPE_PIC) {
            return $query->where('sub_type', $subType)->where('userinfo_id', $userinfoId);
        }
        // 2：实物
        if ($subType == ConsignOrderConst::CONSIGN_SUB_TYPE_ENTITY) {
            $isLeader = AppraiserGroupService::getInstance()->isLeader($userinfoId);
            if ($isLeader) {
                $memberList = AppraiserGroupLogic::getInstance()->memberList($userinfoId)['list'] ?? [];
                $groupAppraiserIds = array_merge($memberList, $groupAppraiserIds);
                $groupAppraiserIds = array_unique($groupAppraiserIds);
                return $query->where('sub_type', $subType)->whereIn('userinfo_id', $groupAppraiserIds);
            } else {
                $groupAppraiserId = AppraiserGroupLogic::getInstance()->getGroupUserinfoId($userinfoId);
                if ($groupAppraiserId) {
                    $groupAppraiserIds[] = $groupAppraiserId;
                }
                return $query->where('sub_type', $subType)->whereIn('userinfo_id', $groupAppraiserIds);
            }
        }
        //  4：组内剩余
        if ($subType == ConsignOrderConst::CONSIGN_SUB_TYPE_GROUP) {
            // 获取组长ID
            $groupAppraiserId = AppraiserGroupLogic::getInstance()->getGroupUserinfoId($userinfoId);
            if ($groupAppraiserId) {
                return $query->where('sub_type', ConsignOrderConst::CONSIGN_SUB_TYPE_ENTITY)
                    ->where('userinfo_id', $groupAppraiserId);
            } else {
                // -99 不存在
                return $query->where('sub_type', ConsignOrderConst::CONSIGN_SUB_TYPE_ENTITY)
                    ->where('userinfo_id', -99);
            }
        }
        // 5：今日待查
        if ($subType == ConsignOrderConst::CONSIGN_SUB_TYPE_TODAY) {
            $todayStartTimestamp = strtotime(date('Y-m-d 00:00:00'));
            return $query
                ->where('sub_type', ConsignOrderConst::CONSIGN_SUB_TYPE_ENTITY)
                ->where('userinfo_id', $userinfoId)
                ->where('accept_time', '<', $todayStartTimestamp);
        }
        return $query;
    }

    /**
     * 构建状态查询
     *
     * @param $query
     * @param $assessState
     * @return mixed
     */
    private function buildAssessState($query, $assessState)
    {
        $query = $query->where('business_id', BusinessConst::BUSINESS_TYPE_CONSIGN_VERIFY);
        // 全部
        if ($assessState == ConsignOrderConst::ASSESS_STATE_ALL) {
            return $query;
        }
        // 0:待查验
        if ($assessState == ConsignOrderConst::ASSESS_STATE_WAIT) {
            return $query->where('state', OrderConst::ORDER_STATE_WAIT_IDENTIFY);
        }
        // 1:通过
        if ($assessState == ConsignOrderConst::ASSESS_STATE_PASS) {
            return $query->where('state', OrderConst::ORDER_STATE_COMPLETE)->where('ident_truth', TemplateConst::TRUTH_REAL);
        }

        // 2:不通过
        if ($assessState == ConsignOrderConst::ASSESS_STATE_REJECT) {
            return $query->where('state', OrderConst::ORDER_STATE_COMPLETE)->where('ident_truth', TemplateConst::TRUTH_CANT_IDENT);
        }

        return $query;
    }
}
